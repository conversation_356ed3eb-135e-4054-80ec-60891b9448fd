[project]
name = "brainless-stats"
version = "0.1.0"
description = "CS2 Demo Analysis Platform"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "core",
    "backend",
    "demo-processing",
    "pandas-stubs>=2.2.3.250527",
]

[tool.uv.sources]
core = { workspace = true }
demo-processing = { workspace = true }
backend = { workspace = true }

[tool.uv.workspace]
members = [
    "packages/backend",
    "packages/demo-processing",
    "packages/core",
]

[tool.basedpyright]
reportUnusedCallResult = false
include = ["packages/**"]
enableTypeIgnoreComments = true

[tool.ruff]
include = ["packages/**/*.py"]

[tool.ruff.lint]
extend-select = ["ALL"]
preview = true
ignore = ["D100", "E501", "ANN401", "D105", "S101", "S311", "S403", "ISC003", "ASYNC109", "S301", "TRY300", "COM812", "CPY001", "S404", "S603", "PT028", "FBT002", "T201", "RUF029", "BLE001", "TRY301", "PLR6301", "G004", "DOC502", "UP037", "F821", "PLC0415", "PLR2004", "S608"]
extend-fixable = ["D301", "D205"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pydoclint]
ignore-one-line-docstrings = true

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true
ignore-fully-untyped = true
mypy-init-return = true
suppress-none-returning = true

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = true

[tool.ruff.lint.per-file-ignores]
"**/tests/*" = ["DOC", "D"]
"**/tests/__init__.py" = ["D104"]

[dependency-groups]
dev = [
    "codespell>=2.4.1",
    "detect-secrets>=1.5.0",
    "pre-commit>=4.2.0",
]
