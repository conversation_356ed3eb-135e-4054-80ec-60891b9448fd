version: "3.8"

services:
  # Redis for demo processing and caching
  redis:
    image: redis:7-alpine
    container_name: brainless-stats-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - brainless-stats-dev

  # Demo Processing Microservice
  demo-processing:
    build:
      context: ./packages/demo-processing
      dockerfile: Dockerfile
      target: development
    container_name: brainless-stats-demo-processing-dev
    environment:
      - DEMO_PROCESSING_REDIS_URL=redis://redis:6379
      - DEMO_PROCESSING_LOG_LEVEL=DEBUG
      - DEMO_PROCESSING_SERVICE_NAME=demo-processing-dev
      - DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=2
      - DEMO_PROCESSING_PROCESSING_TIMEOUT_SECONDS=300
      - DEMO_PROCESSING_EXECUTOR_MAX_WORKERS=2
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./packages/demo-processing/src:/workspace/demo_processing/src:ro
      - ./packages/backend/storage/demos:/workspace/demo_processing/demo_files:ro
      - demo_processing_cache_dev:/home/<USER>/.cache
    restart: unless-stopped
    networks:
      - brainless-stats-dev
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import redis; redis.Redis(host='redis', port=6379, socket_connect_timeout=1).ping()",
        ]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3

  # Main Backend API
  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile.dev
    container_name: brainless-stats-backend-dev
    ports:
      - "8000:8000"
    environment:
      - ENV=development
      - DATABASE_URI=sqlite:///./database.db
      - DEMO_STORAGE_PATH=/app/storage/demos
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
      demo-processing:
        condition: service_healthy
    volumes:
      - ./packages/backend/src:/app/src:ro
      - ./packages/backend/storage:/app/storage
      - ./packages/backend/database.db:/app/database.db
      - backend_cache_dev:/home/<USER>/.cache
    restart: unless-stopped
    networks:
      - brainless-stats-dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3

  # Frontend with API Gateway
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: brainless-stats-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=/app/local.db
      - BACKEND_API_URL=http://backend:8000
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - ./frontend/src:/app/src:ro
      - ./frontend/static:/app/static:ro
      - ./frontend/local.db:/app/local.db
      - frontend_node_modules_dev:/app/node_modules
    restart: unless-stopped
    networks:
      - brainless-stats-dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3

volumes:
  redis_data_dev:
    driver: local
  demo_processing_cache_dev:
    driver: local
  backend_cache_dev:
    driver: local
  frontend_node_modules_dev:
    driver: local

networks:
  brainless-stats-dev:
    driver: bridge
