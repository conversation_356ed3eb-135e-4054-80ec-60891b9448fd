# Contributing to Brainless Stats

Thank you for your interest in contributing to Brainless Stats! This guide will help you get started
with contributing to our CS2 demo analysis platform.

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and **pnpm** 8+
- **Python** 3.13+ and **uv**
- **Docker** and **Docker Compose**
- **Git** for version control

### Setup Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd brainless-stats

# Setup the entire project
pnpm setup

# Start development environment
pnpm dev
```

## 🏗️ Project Structure

```
brainless-stats/
├── frontend/           # SvelteKit frontend
├── backend/           # FastAPI backend
│   └── demo_processing/  # Demo processing microservice
├── scripts/           # Development tools
├── nginx/            # Production proxy configuration
└── docs/             # Documentation
```

## 🛠️ Development Workflow

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Make Your Changes

Follow the coding standards for each part of the project:

#### Frontend (SvelteKit + TypeScript)

- Use TypeScript for all new code
- Follow the existing component structure
- Add tests for new components
- Use Tailwind CSS for styling
- Follow Svelte best practices

#### Backend (FastAPI + Python)

- Use type hints for all functions
- Follow Google docstring convention
- Add comprehensive error handling
- Write unit tests for new endpoints
- Use Pydantic models for data validation

#### Demo Processing (FastStream + Python)

- Use async/await patterns
- Add structured logging
- Handle errors gracefully
- Write tests for parsing logic
- Document message interfaces

### 3. Run Quality Checks

```bash
# Run all quality checks
pnpm ci

# Or run individually
pnpm lint:fix          # Auto-fix linting issues
pnpm format            # Format code
pnpm test              # Run all tests
pnpm test:coverage     # Run with coverage
```

#### Pre-commit Hooks

We use pre-commit hooks to automatically check code quality before commits:

```bash
# Install pre-commit hooks (one-time setup)
pnpm pre-commit:install

# Run pre-commit checks manually
pnpm pre-commit

# Or run specific hooks
uv run pre-commit run ruff --all-files
uv run pre-commit run markdownlint-fix --all-files
```

The pre-commit hooks include:

- **markdownlint-fix**: Fixes markdown formatting issues
- **check-merge-conflict**: Prevents committing merge conflict markers
- **check-json**: Validates JSON file syntax
- **check-yaml**: Validates YAML file syntax
- **check-added-large-files**: Prevents committing large files (>1MB)
- **detect-private-key**: Detects private keys in code
- **validate-pyproject**: Validates Python project configuration
- **codespell**: Checks and fixes spelling errors
- **mdformat**: Formats markdown files consistently
- **markdown-link-check**: Validates links in markdown files
- **prettier**: Formats YAML files
- **ruff**: Lints and formats Python code
- **basedpyright**: Type checks Python code
- **uv-lock**: Updates dependency lock files

### 4. Commit Your Changes

We use conventional commits:

```bash
git add .
git commit -m "feat: add demo upload functionality"

# Commit types:
# feat: new feature
# fix: bug fix
# docs: documentation changes
# style: formatting changes
# refactor: code refactoring
# test: adding tests
# chore: maintenance tasks
```

### 5. Push and Create Pull Request

```bash
git push origin feature/your-feature-name
```

Then create a pull request on GitHub.

## 🧪 Testing Guidelines

### Frontend Testing

```bash
# Unit tests with Vitest
pnpm --filter frontend test

# E2E tests with Playwright
pnpm --filter frontend test:e2e

# Component testing
pnpm --filter frontend test:ui
```

Write tests for:

- New components and their props
- API integration functions
- Utility functions
- User interactions

### Backend Testing

```bash
# Unit tests with pytest
cd backend && uv run pytest

# With coverage
uv run brainless-cli test --coverage
```

Write tests for:

- API endpoints
- Database operations
- Business logic
- Error handling

### Demo Processing Testing

```bash
# Service tests
cd backend/demo_processing && uv run pytest
```

Write tests for:

- Demo parsing logic
- Message handling
- Error scenarios
- Performance edge cases

## 📝 Code Style

### Python

We use **Ruff** for linting and formatting:

```bash
# Check and fix issues
uv run ruff check --fix
uv run ruff format

# Type checking
uv run basedpyright
```

Key conventions:

- Use type hints everywhere
- Follow Google docstring style
- Maximum line length: 100 characters
- Use descriptive variable names
- Prefer composition over inheritance

### TypeScript/JavaScript

We use **ESLint** and **Prettier**:

```bash
# Check and fix issues
pnpm --filter frontend lint --fix
pnpm --filter frontend format
```

Key conventions:

- Use TypeScript for all new code
- Prefer functional components
- Use descriptive prop names
- Follow Svelte naming conventions
- Use Tailwind CSS classes

## 📚 Documentation

### Code Documentation

- **Python**: Use Google-style docstrings
- **TypeScript**: Use JSDoc comments
- **Components**: Document props and usage examples

### README Updates

When adding new features:

1. Update relevant README files
1. Add usage examples
1. Update configuration sections
1. Add troubleshooting notes

### API Documentation

- Backend API is auto-documented via FastAPI
- Update OpenAPI descriptions for new endpoints
- Add request/response examples

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Description**: Clear description of the issue
1. **Steps to Reproduce**: Detailed steps
1. **Expected Behavior**: What should happen
1. **Actual Behavior**: What actually happens
1. **Environment**: OS, Node.js version, Python version
1. **Logs**: Relevant error messages or logs

Use the bug report template when creating issues.

## 💡 Feature Requests

When requesting features:

1. **Use Case**: Describe the problem you're solving
1. **Proposed Solution**: Your suggested approach
1. **Alternatives**: Other solutions you considered
1. **Additional Context**: Screenshots, mockups, etc.

## 🔄 Pull Request Process

### Before Submitting

- [ ] Code follows style guidelines
- [ ] Tests pass locally
- [ ] Documentation is updated
- [ ] Commit messages follow convention
- [ ] Branch is up to date with main

### PR Description

Include:

- **Summary**: What changes were made
- **Motivation**: Why these changes are needed
- **Testing**: How the changes were tested
- **Screenshots**: For UI changes
- **Breaking Changes**: If any

### Review Process

1. **Automated Checks**: CI/CD pipeline runs
1. **Code Review**: Team members review
1. **Testing**: Manual testing if needed
1. **Approval**: At least one approval required
1. **Merge**: Squash and merge to main

## 🚀 Release Process

### Versioning

We use semantic versioning (semver) with a unified versioning system:

- **Major**: Breaking changes (0.1.0 → 1.0.0)
- **Minor**: New features (0.1.0 → 0.2.0)
- **Patch**: Bug fixes (0.1.0 → 0.1.1)

All packages maintain the same version number for consistency.

### Release Commands

```bash
# Check version status across all packages
pnpm version:status

# Create releases (handles everything automatically)
pnpm version:patch      # Bug fixes
pnpm version:minor      # New features  
pnpm version:major      # Breaking changes
```

### Automated Release Process

Each release command automatically:

1. **Updates all package version files** (package.json, pyproject.toml)
1. **Moves `[Unreleased]` changelog entries** to new version section
1. **Creates git commit and tag** with conventional commit message
1. **Creates GitHub release** with extracted changelog notes
1. **Triggers CI/CD pipeline** to build and publish Docker images

### Manual Release Steps

For maintainers who need manual control:

```bash
# 1. Ensure clean working directory
git status

# 2. Update CHANGELOG.md with new features/fixes
# Add entries under [Unreleased] section

# 3. Create release
pnpm version:patch  # or minor/major

# 4. Push changes (if not done automatically)
git push origin main --tags
```

### Pre-Release Checklist

Before creating a release:

- [ ] All tests pass: `pnpm test`
- [ ] Code is properly formatted: `pnpm format`
- [ ] Linting passes: `pnpm lint`
- [ ] CHANGELOG.md has entries under `[Unreleased]`
- [ ] Documentation is updated
- [ ] Breaking changes are documented

### Post-Release

After release:

- [ ] Verify GitHub release was created
- [ ] Check that Docker images were published
- [ ] Update documentation if needed
- [ ] Communicate changes to users

## 🤝 Community Guidelines

### Code of Conduct

- Be respectful and inclusive
- Help others learn and grow
- Provide constructive feedback
- Focus on the code, not the person

### Communication

- **Issues**: For bugs and feature requests
- **Discussions**: For questions and ideas
- **Pull Requests**: For code contributions
- **Discord**: For real-time chat (if available)

## 🛠️ Development Tools

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "svelte.svelte-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-python.python",
    "charliermarsh.ruff",
    "ms-playwright.playwright",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### Useful Commands

```bash
# Development
python tasks.py dev              # Start everything
pnpm dev:frontend               # Frontend only
pnpm dev:backend                # Backend only

# Testing
python tasks.py test --coverage # All tests with coverage
pnpm test:e2e                   # E2E tests

# Quality
python tasks.py lint --fix      # Fix all linting
pnpm format                     # Format all code

# Docker
pnpm docker:up                  # Start Docker environment
pnpm docker:logs                # View logs
pnpm docker:clean               # Clean up
```

## 📞 Getting Help

- **Documentation**: Check README files first
- **Issues**: Search existing issues
- **Discussions**: Ask questions in GitHub Discussions
- **Code**: Look at existing implementations

## 🙏 Recognition

Contributors will be:

- Added to the contributors list
- Mentioned in release notes
- Credited in documentation

Thank you for contributing to Brainless Stats! 🎉
