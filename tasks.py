#!/usr/bin/env python3
"""Brainless Stats - Universal Task Runner

This script provides a unified interface for all development tasks.
It can be used as an alternative to Makefiles and integrates both
frontend (Node.js/pnpm) and backend (Python/uv) tooling.

Usage:
    python tasks.py <command> [options]

Examples:
    python tasks.py setup
    python tasks.py dev
    python tasks.py test --coverage
    python tasks.py clean --all
"""

import subprocess
import sys
from pathlib import Path


def run_command(command: str, cwd: Path | None = None, check: bool = True) -> int:
    """Run a command and return the exit code."""
    print(f"🔧 Running: {command}")
    if cwd:
        print(f"📁 Working directory: {cwd}")

    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
        )
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed with exit code {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n⚠️ Task interrupted by user")
        return 130


def print_help():
    """Print help information."""
    print("""
🚀 Brainless Stats - Universal Task Runner

📋 Available Commands:

Setup & Environment:
  setup                 Complete project setup
  install              Install all dependencies
  clean                Clean containers and cache
  clean:all            Clean everything including volumes

Development:
  dev                  Start development environment (frontend + backend)
  dev:full             Start full Docker environment
  dev:frontend         Start frontend only
  dev:backend          Start backend only
  dev:demo             Start demo processing only

Building:
  build                Build all services
  build:frontend       Build frontend only
  build:backend        Build backend only
  build:docker         Build Docker images

Testing:
  test                 Run all tests
  test:frontend        Run frontend tests
  test:backend         Run backend tests
  test:e2e             Run end-to-end tests
  test:coverage        Run tests with coverage

Code Quality:
  lint                 Run linting for all services
  lint:fix             Auto-fix linting issues
  format               Format code for all services
  check                Run type checking

Utilities:
  logs                 View service logs
  status               Show service status
  health               Check service health
  shell:backend        Open backend shell
  shell:frontend       Open frontend shell
  shell:demo           Open demo processing shell
  shell:redis          Open Redis shell

Production:
  start:prod           Start production environment
  stop                 Stop all services

📖 Examples:
  python tasks.py setup
  python tasks.py dev
  python tasks.py test:coverage
  python tasks.py lint:fix
  python tasks.py clean:all

💡 Tip: You can also use pnpm scripts directly:
  pnpm dev
  pnpm test
  pnpm lint
""")


def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        print_help()
        return 0

    command = sys.argv[1]
    project_root = Path(__file__).parent

    # Handle help
    if command in ["help", "--help", "-h"]:
        print_help()
        return 0

    # Map commands to pnpm scripts
    command_map = {
        # Setup & Environment
        "setup": "pnpm setup",
        "install": "pnpm install",
        "clean": "pnpm clean",
        "clean:all": "pnpm clean:all",
        # Development
        "dev": "pnpm dev",
        "dev:full": "pnpm dev:full",
        "dev:frontend": "pnpm dev:frontend",
        "dev:backend": "pnpm dev:backend",
        "dev:demo": "pnpm dev:demo-processing",
        # Building
        "build": "pnpm build",
        "build:frontend": "pnpm build:frontend",
        "build:backend": "pnpm build:backend",
        "build:docker": "pnpm build:docker",
        # Testing
        "test": "pnpm test",
        "test:frontend": "pnpm test:frontend",
        "test:backend": "pnpm test:backend",
        "test:e2e": "pnpm test:e2e",
        "test:coverage": "pnpm test:coverage",
        # Code Quality
        "lint": "pnpm lint",
        "lint:fix": "pnpm lint:fix",
        "format": "pnpm format",
        "check": "pnpm lint",  # Alias for lint
        # Utilities
        "logs": "pnpm logs",
        "status": "pnpm status",
        "health": "pnpm health",
        "shell:backend": "pnpm shell:backend",
        "shell:frontend": "pnpm shell:frontend",
        "shell:demo": "pnpm shell:demo-processing",
        "shell:redis": "pnpm shell:redis",
        # Production
        "start:prod": "pnpm start:prod",
        "stop": "pnpm stop",
    }

    # Handle special cases with options
    if command == "test" and "--coverage" in sys.argv:
        mapped_command = "pnpm test:coverage"
    elif command == "lint" and "--fix" in sys.argv:
        mapped_command = "pnpm lint:fix"
    elif command == "clean" and "--all" in sys.argv:
        mapped_command = "pnpm clean:all"
    else:
        mapped_command = command_map.get(command)

    if not mapped_command:
        print(f"❌ Unknown command: {command}")
        print("💡 Run 'python tasks.py help' to see available commands")
        return 1

    # Execute the command
    return run_command(mapped_command, cwd=project_root)


if __name__ == "__main__":
    sys.exit(main())
