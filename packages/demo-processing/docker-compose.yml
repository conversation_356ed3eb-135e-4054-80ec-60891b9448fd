services:
  redis:
    image: redis:7-alpine
    container_name: demo-processing-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  demo-processing:
    build:
      context: ..
      dockerfile: demo_processing/Dockerfile
    container_name: demo-processing-service
    environment:
      - DEMO_PROCESSING_REDIS_URL=redis://redis:6379
      - DEMO_PROCESSING_LOG_LEVEL=INFO
      - DEMO_PROCESSING_SERVICE_NAME=demo-processing
      - DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=3
      - DEMO_PROCESSING_PROCESSING_TIMEOUT_SECONDS=300
      - DEMO_PROCESSING_EXECUTOR_MAX_WORKERS=4
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      # Mount demo files directory (adjust path as needed)
      - ../storage/demos:/workspace/demo_processing/demo_files:ro
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import redis; redis.Redis(host='redis', port=6379, socket_connect_timeout=1).ping()",
        ]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3

volumes:
  redis_data:
    driver: local
