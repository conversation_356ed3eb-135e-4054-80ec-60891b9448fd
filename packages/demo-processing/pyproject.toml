[project]
name = "demo-processing"
version = "0.1.0"
description = "CS2 Demo Processing Microservice"

authors = [
    { name = "Maximilian Noller", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "anyio>=4.0.0",
    "awpy>=2.0.2",
    "demoparser2>=0.38.0",
    "faststream[redis]>=0.5.42",
    "pydantic>=2.10.6",
    "pydantic-settings>=2.8.1",
    "structlog>=24.5.0",
    "core",
]

[dependency-groups]
dev = [
    "basedpyright>=1.29.1",
    "pytest>=8.3.5",
    "ruff>=0.9.9",
]

[tool.uv.sources]
core = { workspace = true }

[project.scripts]
demo-processing = "demo_processing.main:main"

[tool.basedpyright]
reportUnusedCallResult = false
include = ["src"]
stubPath = "typings"

[tool.ruff]
target-version = "py313"

[tool.ruff.lint]
extend-select = ["D", "DOC", "UP", "FURB", "RUF", "TRY", "F", "E", "F", "PERF",
                 "N", "NPY", "C90", "I", "PTH", "TC", "TD", "SIM", "RET", "Q",
                 "PT", "PYI", "T20", "PIE", "LOG", "ISC", "FA", "FIX", "EM", "DTZ",
                 "C4", "A", "B", "BLE", "S", "ASYNC", "ANN", "YTT", "FAST", "ERA", "B",
                 "TC"]
preview = true
ignore = ["D100", "E501", "ANN401", "D105", "S101", "S311", "S403", "ISC003", "ASYNC109", "S301"]
extend-fixable = ["D301", "D205"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pydoclint]
ignore-one-line-docstrings = true

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
