# Demo Processing Microservice Dockerfile
FROM python:3.13-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV UV_CACHE_DIR=/tmp/uv-cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Set working directory
WORKDIR /workspace

# Copy workspace files (from backend directory)
COPY pyproject.toml uv.lock README.md ./
COPY demo_processing/pyproject.toml ./demo_processing/
COPY src/ ./src/

# Copy source code
COPY demo_processing/src/ ./demo_processing/src/

# Create non-root user and set ownership efficiently
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /workspace

# Set working directory to demo_processing
WORKDIR /workspace/demo_processing

# Switch to non-root user
USER app

# Set UV cache directory to user's home
ENV UV_CACHE_DIR=/home/<USER>/.cache/uv

# Development target
FROM base as development
RUN uv sync --frozen
EXPOSE 8000
CMD ["uv", "run", "python", "-m", "demo_processing.main"]

# Production target
FROM base as production
RUN uv sync --frozen --no-dev
EXPOSE 8000
CMD ["uv", "run", "python", "-m", "demo_processing.main"]

# Default target
FROM development

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD uv run python -c "import redis; redis.Redis(host='redis', port=6379, socket_connect_timeout=1).ping()" || exit 1
