# Demo Processing Microservice

FastStream-based microservice for processing CS2 demo files.

## 🏗️ Architecture

- **FastStream**: Modern async microservice framework
- **Redis**: Message broker and result storage
- **DemoParser2**: CS2 demo file parsing
- **AWPY**: Advanced CS2 analytics
- **AnyIO**: Async utilities and thread management
- **Structured Logging**: Comprehensive logging with structlog

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- Redis server
- UV package manager

### Development Setup

```bash
# From project root
pnpm setup

# Or from backend directory
cd backend
uv sync

# Or directly in demo processing
cd backend/demo_processing
uv sync
```

### Running the Service

```bash
# From project root (recommended)
pnpm dev:demo-processing

# Or using the backend CLI
cd backend
uv run brainless-cli dev demo-processing

# Or directly
cd backend/demo_processing
uv run python -m demo_processing.main
```

## 🛠️ CLI Integration

The demo processing service is managed through the main backend CLI:

```bash
cd backend

# Start demo processing service
uv run brainless-cli dev demo-processing

# View logs
uv run brainless-cli logs demo-processing

# Open shell in container
uv run brainless-cli shell demo-processing

# Check service status
uv run brainless-cli status
```

## 📁 Project Structure

```
demo_processing/
├── src/demo_processing/
│   ├── main.py              # Service entry point
│   ├── service.py           # FastStream application
│   ├── config.py            # Configuration management
│   ├── models.py            # Pydantic data models
│   ├── parser.py            # Demo parsing logic
│   ├── async_utils.py       # Async utilities
│   └── logging_config.py    # Logging setup
├── Dockerfile               # Multi-stage container
├── docker-compose.yml       # Local development
├── pyproject.toml           # Dependencies and configuration
└── README.md               # This file
```

## 🔧 Configuration

### Environment Variables

```bash
# Redis Configuration
DEMO_PROCESSING_REDIS_URL=redis://localhost:6379

# Logging
DEMO_PROCESSING_LOG_LEVEL=INFO

# Service Settings
DEMO_PROCESSING_SERVICE_NAME=demo-processing
DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=3
DEMO_PROCESSING_PROCESSING_TIMEOUT_SECONDS=300
DEMO_PROCESSING_EXECUTOR_MAX_WORKERS=4
```

### Performance Tuning

The service includes several performance optimizations:

- **Concurrency Control**: Limits concurrent demo processing
- **Thread Pool**: Dedicated thread pool for CPU-bound operations
- **Timeout Handling**: Configurable timeouts for long-running operations
- **Memory Management**: Efficient memory usage for large demo files

## 📨 Message Interface

### Demo Processing Request

```python
class DemoProcessingRequest(BaseModel):
    request_id: str
    demo_file_path: str
    options: dict[str, Any] = {}
```

### Demo Processing Result

```python
class DemoProcessingResult(BaseModel):
    request_id: str
    success: bool
    demo_data: DemoData | None = None
    error_message: str | None = None
    processing_time_seconds: float
```

### Usage Example

```python
from backend.clients.demo_processing_client import DemoProcessingClient

client = DemoProcessingClient()

# Process a demo file
result = await client.process_demo("/path/to/demo.dem")

if result.success:
    print(f"Processed demo: {result.demo_data.map_info.name}")
else:
    print(f"Processing failed: {result.error_message}")
```

## 🧪 Testing

```bash
# Run tests
cd backend/demo_processing
uv run pytest

# Run with coverage
uv run pytest --cov=demo_processing

# Run specific test
uv run pytest tests/test_parser.py
```

## 🔍 Code Quality

### Linting and Formatting

```bash
# Check code quality
uv run ruff check

# Auto-fix issues
uv run ruff check --fix

# Format code
uv run ruff format

# Type checking
uv run basedpyright
```

## 🐳 Docker

### Development

```bash
# Build development image
docker build --target development -t demo-processing:dev .

# Run with Redis
docker-compose up
```

### Production

```bash
# Build production image
docker build --target production -t demo-processing:prod .

# Run production container
docker run --env-file .env demo-processing:prod
```

## 📊 Monitoring

### Health Checks

The service provides health check functionality:

```python
# Health check via message
health_status = await client.health_check()
print(health_status)  # {"status": "healthy", "service": "demo-processing", ...}
```

### Logging

Structured logging with contextual information:

```python
logger.info(
    "Demo processing started",
    request_id=request.request_id,
    file_path=request.demo_file_path,
    file_size=file_size
)
```

### Metrics

Key metrics tracked:

- Processing time per demo
- Success/failure rates
- Concurrent processing count
- Memory usage
- Queue depth

## 🔄 Message Flow

1. **Request**: Backend sends `DemoProcessingRequest` to `demo.processing.requests`
1. **Processing**: Service processes demo file asynchronously
1. **Progress**: Optional progress updates during processing
1. **Result**: Service sends `DemoProcessingResult` to `demo.processing.results`
1. **Health**: Health checks via `demo.processing.health.check`

## ⚡ Performance

### Optimization Features

- **Async Processing**: Non-blocking demo file processing
- **Thread Pool**: CPU-bound operations in dedicated threads
- **Memory Streaming**: Efficient handling of large demo files
- **Timeout Management**: Prevents hanging on corrupted files
- **Graceful Shutdown**: Proper cleanup on service termination

### Benchmarks

Typical performance on modern hardware:

- **Small demos** (< 50MB): 2-5 seconds
- **Medium demos** (50-200MB): 5-15 seconds
- **Large demos** (> 200MB): 15-60 seconds

## 🚀 Deployment

### Production Checklist

- [ ] Configure Redis connection
- [ ] Set appropriate resource limits
- [ ] Configure logging level
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategy for Redis
- [ ] Set up log aggregation

### Scaling

The service can be scaled horizontally:

```yaml
# docker-compose.yml
services:
  demo-processing:
    image: demo-processing:prod
    deploy:
      replicas: 3
    environment:
      - DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=2
```

## 🤝 Contributing

1. Follow the async/await patterns
1. Add comprehensive error handling
1. Include structured logging
1. Write tests for new features
1. Update documentation

```bash
# Before committing
uv run ruff check --fix
uv run ruff format
uv run basedpyright
uv run pytest
```

## 📚 Resources

- [FastStream Documentation](https://faststream.airt.ai/)
- [DemoParser2](https://github.com/LaihoE/demoparser2)
- [AWPY](https://github.com/pnxenopoulos/awpy)
- [Redis Documentation](https://redis.io/documentation)
- [AnyIO Documentation](https://anyio.readthedocs.io/)
