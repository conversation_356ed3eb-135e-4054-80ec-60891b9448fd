"""Configuration settings for the demo processing service."""

from functools import lru_cache
from pathlib import Path
from typing import ClassVar

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Configuration settings for demo processing service."""

    # Redis Configuration
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis connection URL",
    )

    # Service Configuration
    service_name: str = Field(
        default="demo-processing", description="Name of the service"
    )

    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")

    # Demo Storage Configuration
    demo_storage_path: Path = Field(
        default=Path("/workspace/demo_processing/demo_files"),
        description="Path to demo storage directory",
    )

    # Processing Configuration
    max_concurrent_processing: int = Field(
        default=3, description="Maximum number of demos to process concurrently"
    )

    processing_timeout_seconds: int = Field(
        default=300, description="Timeout for demo processing in seconds"
    )

    # Async Configuration
    executor_max_workers: int = Field(
        default=4, description="Maximum number of executor workers for CPU-bound tasks"
    )

    # Retry Configuration
    max_retries: int = Field(
        default=3, description="Maximum number of retries for failed processing"
    )

    retry_delay_seconds: float = Field(
        default=1.0, description="Initial delay between retries in seconds"
    )

    retry_backoff_multiplier: float = Field(
        default=2.0, description="Multiplier for exponential backoff"
    )

    model_config: ClassVar[SettingsConfigDict] = SettingsConfigDict(
        env_file=".env",
        env_prefix="DEMO_PROCESSING_",
        case_sensitive=False,
        extra="ignore",
    )


@lru_cache
def get_settings() -> Settings:
    """Get application settings.

    Returns:
        Settings instance
    """
    return Settings()
