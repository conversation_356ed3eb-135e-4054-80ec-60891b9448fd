"""Brainless Stats CLI - Development and Operations Tool."""

import json
import subprocess
import time
from pathlib import Path
from typing import Annotated

import httpx
import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

app = typer.Typer(
    name="brainless-cli",
    help="Brainless Stats Development and Operations CLI",
    rich_markup_mode="rich",
)

console = Console()


def run_command(
    command: str,
    cwd: Path | None = None,
    *,
    check: bool = True,
    capture_output: bool = False,
) -> subprocess.CompletedProcess[str]:
    """Run a shell command with proper error handling."""
    try:
        return subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=capture_output,
            text=True,
        )
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Command failed:[/red] {command}")
        console.print(f"[red]Error:[/red] {e}")
        if e.stdout:
            console.print(f"[yellow]stdout:[/yellow] {e.stdout}")
        if e.stderr:
            console.print(f"[red]stderr:[/red] {e.stderr}")
        raise typer.Exit(1) from e


def check_docker() -> bool:
    """Check if Docker is running."""
    try:
        _ = run_command("docker info", capture_output=True)
        return True
    except subprocess.CalledProcessError:
        console.print(
            "[red]Docker is not running. Please start Docker and try again.[/red]"
        )
        return False


def get_project_root() -> Path:
    """Get the project root directory."""
    current = Path(__file__).parent
    while current != current.parent:
        if (current / "package.json").exists():
            return current
        current = current.parent
    return Path.cwd()


@app.command()
def setup(
    *,
    force: Annotated[
        bool, typer.Option("--force", "-f", help="Force reinstall dependencies")
    ] = False,
) -> None:
    """Set up the development environment."""
    console.print(
        Panel.fit(
            "🚀 Setting up Brainless Stats development environment", style="bold blue"
        )
    )

    project_root = get_project_root()
    backend_dir = project_root / "packages/backend"

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        # Install backend dependencies
        task = progress.add_task("Installing backend dependencies...", total=None)
        if force:
            _ = run_command("uv sync --reinstall", cwd=backend_dir)
        else:
            _ = run_command("uv sync", cwd=backend_dir)
        progress.update(task, description="✅ Backend dependencies installed")

        # Create environment files
        task = progress.add_task("Setting up environment files...", total=None)

        env_files = [
            (project_root / ".env", project_root / ".env.development"),
            (backend_dir / ".env", backend_dir / ".env.example"),
        ]

        for env_file, example_file in env_files:
            if not env_file.exists() and example_file.exists():
                _ = env_file.write_text(example_file.read_text())
                console.print(f"Created {env_file.relative_to(project_root)}")

        progress.update(task, description="✅ Environment files configured")

    console.print("[green]✅ Setup completed successfully![/green]")
    console.print(
        "You can now run [bold]brainless-cli dev[/bold] to start the development environment"
    )


@app.command()
def dev(
    service: Annotated[
        str | None,
        typer.Argument(help="Specific service to run (backend, demo-processing)"),
    ] = None,
    *,
    detached: Annotated[
        bool, typer.Option("--detach", "-d", help="Run in detached mode")
    ] = False,
) -> None:
    """Start the development environment."""
    if not check_docker():
        raise typer.Exit(1)

    project_root = get_project_root()

    if service == "backend":
        console.print("🚀 Starting backend development server...")
        _ = run_command(
            "uv run fastapi dev src/backend/main.py --host 0.0.0.0 --port 8000",
            cwd=project_root / "packages/backend",
        )
    elif service == "demo-processing":
        console.print("🚀 Starting demo processing service...")
        _ = run_command(
            "docker-compose up --build" + (" -d" if detached else ""),
            cwd=project_root / "packages/backend" / "demo_processing",
        )
    else:
        console.print("🚀 Starting full development environment...")
        cmd = f"docker-compose -f docker-compose.dev.yml up --build{' -d' if detached else ''}"
        _ = run_command(cmd, cwd=project_root)


@app.command()
def stop() -> None:
    """Stop all services."""
    if not check_docker():
        raise typer.Exit(1)

    project_root = get_project_root()

    console.print("🛑 Stopping all services...")

    # Stop main development environment
    _ = run_command(
        "docker-compose -f docker-compose.dev.yml down",
        cwd=project_root,
        check=False,
    )

    # Stop production environment
    _ = run_command(
        "docker-compose -f docker-compose.prod.yml down",
        cwd=project_root,
        check=False,
    )

    # Stop demo processing
    _ = run_command(
        "docker-compose down",
        cwd=project_root / "packages/backend" / "demo_processing",
        check=False,
    )

    console.print("[green]✅ All services stopped[/green]")


@app.command()
def logs(
    service: Annotated[
        str | None, typer.Argument(help="Service to show logs for")
    ] = None,
    *,
    follow: Annotated[
        bool, typer.Option("--follow", "-f", help="Follow log output")
    ] = True,
) -> None:
    """Show service logs."""
    if not check_docker():
        raise typer.Exit(1)

    project_root = get_project_root()

    if service:
        cmd = f"docker-compose -f docker-compose.dev.yml logs{' -f' if follow else ''} {service}"
    else:
        cmd = f"docker-compose -f docker-compose.dev.yml logs{' -f' if follow else ''}"

    _ = run_command(cmd, cwd=project_root)


@app.command()
def shell(
    service: Annotated[
        str,
        typer.Argument(
            help="Service to open shell in (backend, frontend, demo-processing, redis)"
        ),
    ],
) -> None:
    """Open a shell in a service container."""
    if not check_docker():
        raise typer.Exit(1)

    _ = get_project_root()

    service_shells = {
        "backend": ("brainless-stats-backend-dev", "/bin/bash"),
        "frontend": ("brainless-stats-frontend-dev", "/bin/sh"),
        "demo-processing": ("brainless-stats-demo-processing-dev", "/bin/bash"),
        "redis": ("brainless-stats-redis-dev", "/bin/sh"),
    }

    if service not in service_shells:
        console.print(f"[red]Unknown service: {service}[/red]")
        console.print(f"Available services: {', '.join(service_shells.keys())}")
        raise typer.Exit(1)

    container_name, shell = service_shells[service]
    console.print(f"🐚 Opening shell in {service} container...")
    _ = run_command(f"docker exec -it {container_name} {shell}")


@app.command()
def test(
    service: Annotated[
        str | None, typer.Argument(help="Service to test (backend, demo-processing)")
    ] = None,
    *,
    coverage: Annotated[
        bool, typer.Option("--coverage", "-c", help="Run with coverage")
    ] = False,
) -> None:
    """Run tests."""
    project_root = get_project_root()

    if service == "backend":
        console.print("🧪 Running backend tests...")
        cmd = "uv run pytest"
        if coverage:
            cmd += " --cov=backend --cov-report=html --cov-report=term"
        _ = run_command(cmd, cwd=project_root / "packages/backend")
    elif service == "demo-processing":
        console.print("🧪 Running demo processing tests...")
        cmd = "uv run pytest"
        if coverage:
            cmd += " --cov=demo_processing --cov-report=html --cov-report=term"
        _ = run_command(cmd, cwd=project_root / "packages/backend" / "demo_processing")
    else:
        console.print("🧪 Running all backend tests...")
        test(service="backend", coverage=coverage)
        test(service="demo-processing", coverage=coverage)


@app.command()
def lint(
    fix: Annotated[
        bool, typer.Option("--fix", help="Auto-fix issues where possible")
    ] = False,
) -> None:
    """Run linting."""
    project_root = get_project_root()
    backend_dir = project_root / "packages/backend"

    console.print("🔍 Running linting...")

    # Run ruff check
    cmd = "uv run ruff check"
    if fix:
        cmd += " --fix"
    _ = run_command(cmd, cwd=backend_dir)

    # Run type checking
    console.print("🔍 Running type checking...")
    _ = run_command("uv run basedpyright", cwd=backend_dir)

    console.print("[green]✅ Linting completed[/green]")


@app.command()
def format_code() -> None:
    """Format code."""
    project_root = get_project_root()
    backend_dir = project_root / "packages/backend"

    console.print("🎨 Formatting code...")
    _ = run_command("uv run ruff format", cwd=backend_dir)
    console.print("[green]✅ Code formatted[/green]")


@app.command()
def build(
    target: Annotated[
        str, typer.Argument(help="Build target (docker, prod)")
    ] = "docker",
) -> None:
    """Build the application."""
    if not check_docker():
        raise typer.Exit(1)

    project_root = get_project_root()

    if target == "docker":
        console.print("🏗️ Building Docker images...")
        _ = run_command("docker-compose -f docker-compose.prod.yml build", cwd=project_root)
    elif target == "prod":
        console.print("🏗️ Building for production...")
        _ = run_command("docker-compose -f docker-compose.prod.yml build", cwd=project_root)
    else:
        console.print(f"[red]Unknown build target: {target}[/red]")
        raise typer.Exit(1)

    console.print("[green]✅ Build completed[/green]")


@app.command()
def clean(
    all_data: Annotated[
        bool, typer.Option("--all", help="Remove all data including volumes")
    ] = False,
) -> None:
    """Clean up containers and optionally volumes."""
    if not check_docker():
        raise typer.Exit(1)

    if all_data:
        confirm = typer.confirm(
            "This will remove all containers, volumes, and data. Are you sure?"
        )
        if not confirm:
            console.print("Cancelled.")
            raise typer.Exit(0)

    project_root = get_project_root()

    console.print("🧹 Cleaning up...")

    # Stop and remove containers
    stop()

    if all_data:
        # Remove volumes
        _ = run_command(
            "docker-compose -f docker-compose.dev.yml down -v --remove-orphans",
            cwd=project_root,
            check=False,
        )
        _ = run_command(
            "docker-compose -f docker-compose.prod.yml down -v --remove-orphans",
            cwd=project_root,
            check=False,
        )
        _ = run_command("docker system prune -f", check=False)

    console.print("[green]✅ Cleanup completed[/green]")


@app.command()
def status() -> None:
    """Show service status."""
    if not check_docker():
        raise typer.Exit(1)

    project_root = get_project_root()

    console.print("📊 Service Status")

    # Get container status
    try:
        result = run_command(
            "docker-compose -f docker-compose.dev.yml ps --format json",
            cwd=project_root,
            capture_output=True,
        )

        table = Table(title="Development Services")
        table.add_column("Service", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Ports", style="yellow")

        if result.stdout.strip():
            containers = [
                json.loads(line) for line in result.stdout.strip().split("\n")
            ]

            for container in containers:
                status = container.get("State", "unknown")
                service = container.get("Service", "unknown")
                ports = container.get("Publishers", [])
                port_str = ", ".join(
                    [
                        f"{p.get('PublishedPort', '')}:{p.get('TargetPort', '')}"
                        for p in ports
                        if p.get("PublishedPort")
                    ]
                )

                status_style = "green" if status == "running" else "red"
                table.add_row(
                    service, f"[{status_style}]{status}[/{status_style}]", port_str
                )
        else:
            table.add_row("No services running", "", "")

        console.print(table)

    except (subprocess.CalledProcessError, OSError, ValueError) as e:
        console.print(f"[red]Error getting status: {e}[/red]")


@app.command()
def health() -> None:
    """Check service health."""
    console.print("🏥 Checking service health...")

    services = [
        ("Frontend", "http://localhost:3000/health"),
        ("Backend", "http://localhost:8000/health"),
    ]

    table = Table(title="Health Check Results")
    table.add_column("Service", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Response Time", style="yellow")

    for service_name, url in services:
        try:
            start_time = time.time()
            response = httpx.get(url, timeout=5)
            response_time = f"{(time.time() - start_time) * 1000:.0f}ms"

            if response.status_code == 200:
                table.add_row(service_name, "[green]Healthy[/green]", response_time)
            else:
                table.add_row(
                    service_name,
                    f"[red]Unhealthy ({response.status_code})[/red]",
                    response_time,
                )
        except (OSError, ValueError, TimeoutError) as e:
            table.add_row(service_name, f"[red]Error: {str(e)[:50]}[/red]", "N/A")

    console.print(table)


if __name__ == "__main__":
    app()
