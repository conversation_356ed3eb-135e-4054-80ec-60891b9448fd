"""Demo file and processing job models."""

from __future__ import annotations

from datetime import UTC, datetime

from sqlmodel import Field, Relationship, SQLModel

from .enums import DemoFileStatus, ProcessingJobStatus


class DemoFileBase(SQLModel):
    """Base demo file data."""

    match_id: str = Field(..., description="Match identifier from CSGO")
    file_path: str = Field(..., description="Path to the demo file on disk")
    status: DemoFileStatus = Field(
        default=DemoFileStatus.PENDING_DOWNLOAD,
        description="Current status of the demo file",
    )
    error_message: str | None = Field(
        default=None, description="Error message if processing failed"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record last update time",
    )


class ProcessingJobBase(SQLModel):
    """Base processing job data."""

    demo_file_id: int = Field(
        ..., foreign_key="demofile.id", description="Reference to the demo file being processed"
    )
    priority: int = Field(
        default=0, description="Job priority (higher = more important)"
    )
    status: ProcessingJobStatus = Field(
        default=ProcessingJobStatus.QUEUED,
        description="Current status of the processing job",
    )
    attempts: int = Field(default=0, description="Number of processing attempts")
    last_error: str | None = Field(
        default=None, description="Last error message if processing failed"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record last update time",
    )


class DemoFile(DemoFileBase, table=True):
    """Demo file data."""

    id: int | None = Field(default=None, primary_key=True)
    match: "Match | None" = Relationship(back_populates="demo_file")  # type: ignore[name-defined]
    processing_jobs: list["ProcessingJob"] = Relationship(back_populates="demo_file")


class ProcessingJob(ProcessingJobBase, table=True):
    """Processing job data."""

    id: int | None = Field(default=None, primary_key=True)
    demo_file: "DemoFile" = Relationship(back_populates="processing_jobs")
