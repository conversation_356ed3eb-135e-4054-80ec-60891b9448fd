"""Match-related database models."""

from __future__ import annotations

from datetime import UTC, datetime

from sqlmodel import Field, Relationship, SQLModel

from .enums import Team, WinReason


class MatchPlayerBase(SQLModel):
    """Data about a player in a match."""

    steam_id: str = Field(..., description="Steam ID of the player")
    name: str = Field(..., description="In-game name of the player")
    team: Team = Field(default=Team.UNKNOWN, description="Team the player was on")
    match_id: int | None = Field(
        default=None, foreign_key="match.id", description="Match this player was in"
    )


class MatchRoundBase(SQLModel):
    """Data about a round in a match."""

    round_number: int = Field(..., description="Round number")
    winner: Team = Field(default=Team.UNKNOWN, description="Team that won the round")
    win_reason: WinReason = Field(
        default=WinReason.UNKNOWN, description="Reason for the round win"
    )
    ct_score: int = Field(default=0, description="CT score after this round")
    t_score: int = Field(default=0, description="T score after this round")
    match_id: int | None = Field(
        default=None, foreign_key="match.id", description="Match this round was in"
    )


class MatchBase(SQLModel):
    """Data about a match extracted from a demo file."""

    map_name: str = Field(..., description="Name of the map played")
    demo_file_id: int | None = Field(
        default=None,
        foreign_key="demofile.id",
        description="Demo file this match data was extracted from",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Record creation time",
    )


class MatchPlayer(MatchPlayerBase, table=True):
    """Data about a player in a match."""

    id: int | None = Field(default=None, primary_key=True)
    match: "Match" = Relationship(back_populates="players")


class MatchRound(MatchRoundBase, table=True):
    """Data about a round in a match."""

    id: int | None = Field(default=None, primary_key=True)
    match: "Match" = Relationship(back_populates="rounds")


class Match(MatchBase, table=True):
    """Data about a match extracted from a demo file."""

    id: int | None = Field(default=None, primary_key=True)
    demo_file: "DemoFile" = Relationship(back_populates="match")  # type: ignore[name-defined]
    players: list["MatchPlayer"] = Relationship(back_populates="match")
    rounds: list["MatchRound"] = Relationship(back_populates="match")
