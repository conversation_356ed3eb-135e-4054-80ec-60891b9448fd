"""Integration test for API endpoints using testcontainers.

This module tests the API endpoints with a real PostgreSQL database to ensure
that the complete request-response cycle works correctly with raw SQL operations.
"""

import pytest
from fastapi.testclient import TestClient
from httpx import ASGITransport, AsyncClient
from sqlalchemy import Connection, create_engine, text
from testcontainers.postgres import PostgresContainer

from backend.database import get_connection_dependency, init_db
from backend.main import app


@pytest.fixture(scope="module")
def postgres_container():
    """Start a PostgreSQL container for integration testing."""
    with PostgresContainer("postgres:15-alpine") as postgres:
        yield postgres


@pytest.fixture(scope="module")
def postgres_engine(postgres_container):
    """Create a SQLAlchemy engine connected to the PostgreSQL container."""
    connection_url = postgres_container.get_connection_url()
    engine = create_engine(connection_url, echo=True)

    # Initialize database tables using our init_db function
    import backend.database

    original_engine = backend.database.engine
    backend.database.engine = engine
    try:
        init_db()
    finally:
        backend.database.engine = original_engine

    return engine


@pytest.fixture()
def postgres_connection(postgres_engine):
    """Create a fresh database connection for each test."""
    with postgres_engine.connect() as conn:
        # Clean up any existing data before each test
        conn.execute(
            text(
                'TRUNCATE TABLE match_round, match_player, "match", processing_job, demo_file, tracking_details, "user" RESTART IDENTITY CASCADE'
            )
        )
        conn.commit()
        yield conn


@pytest.fixture()
def test_client(postgres_connection: Connection):
    """Create a test client with database dependency override."""

    def get_test_connection():
        yield postgres_connection

    app.dependency_overrides[get_connection_dependency] = get_test_connection

    with TestClient(app) as client:
        yield client

    app.dependency_overrides.clear()


@pytest.fixture()
async def async_test_client(postgres_connection: Connection):
    """Create an async test client with database dependency override."""

    def get_test_connection():
        yield postgres_connection

    app.dependency_overrides[get_connection_dependency] = get_test_connection

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        yield client

    app.dependency_overrides.clear()


class TestAPIIntegration:
    """Test class for API integration with real PostgreSQL database."""

    def test_health_endpoint(self, test_client: TestClient):
        """Test that the health endpoint works."""
        response = test_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "backend"

    def test_user_by_steam_id_not_found(self, test_client: TestClient):
        """Test the user endpoint when user doesn't exist."""
        steam_id = "76561198079047062"
        response = test_client.get(f"/api/users/steamid/{steam_id}")
        assert response.status_code == 404
        assert "User not found" in response.json()["detail"]

    def test_user_by_steam_id_without_tracking(
        self, test_client: TestClient, postgres_connection: Connection
    ):
        """Test the user endpoint with user but no tracking details."""
        # Create a user without tracking details using raw SQL
        result = postgres_connection.execute(
            text("""
            INSERT INTO "user" (steam_id, username, profile_picture_url, created_at)
            VALUES (:steam_id, :username, :profile_picture_url, NOW())
            RETURNING id
        """),
            {
                "steam_id": "76561198079047062",
                "username": "test_user",
                "profile_picture_url": "https://example.com/pic.jpg",
            },
        )
        result.fetchone()
        postgres_connection.commit()

        # Test the endpoint
        steam_id = "76561198079047062"
        response = test_client.get(f"/api/users/steamid/{steam_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["steam_id"] == steam_id
        assert data["username"] == "test_user"

    def test_user_read_with_tracking(
        self, test_client: TestClient, postgres_connection: Connection
    ):
        """Test the user read endpoint that includes tracking information."""
        # Create a user with tracking details using raw SQL
        user_result = postgres_connection.execute(
            text("""
            INSERT INTO "user" (steam_id, username, profile_picture_url, created_at)
            VALUES (:steam_id, :username, :profile_picture_url, NOW())
            RETURNING id
        """),
            {
                "steam_id": "76561198079047062",
                "username": "test_user",
                "profile_picture_url": "https://example.com/pic.jpg",
            },
        )
        user_row = user_result.fetchone()
        user_id = user_row.id

        postgres_connection.execute(
            text("""
            INSERT INTO tracking_details (authentication_code, initial_match_token, user_id)
            VALUES (:auth_code, :token, :user_id)
        """),
            {
                "auth_code": "auth123",
                "token": "token456",
                "user_id": user_id,
            },
        )
        postgres_connection.commit()

        # Test the user read endpoint that includes tracking info
        response = test_client.get(f"/api/users/{user_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["steam_id"] == "76561198079047062"
        assert data["username"] == "test_user"
        assert data["has_tracking"] is True

    def test_demo_file_operations(
        self, test_client: TestClient, postgres_connection: Connection
    ):
        """Test demo file CRUD operations through API."""
        # Test listing empty demo files
        response = test_client.get("/api/demos")
        assert response.status_code == 200
        assert response.json() == []

        # Create a demo file directly in database for testing
        demo_result = postgres_connection.execute(
            text("""
            INSERT INTO demo_file (match_id, file_path, status, created_at, updated_at)
            VALUES (:match_id, :file_path, :status, NOW(), NOW())
            RETURNING id
        """),
            {
                "match_id": "test_match_123",
                "file_path": "/path/to/demo.dem",
                "status": "PENDING_DOWNLOAD",
            },
        )
        demo_result.fetchone()
        postgres_connection.commit()

        # Test listing demo files
        response = test_client.get("/api/demos")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["match_id"] == "test_match_123"
        assert data[0]["status"] == "PENDING_DOWNLOAD"

        # Test getting demo status by match ID
        response = test_client.get("/api/matches/test_match_123/demo/status")
        assert response.status_code == 200
        data = response.json()
        assert data["match_id"] == "test_match_123"
        assert data["status"] == "PENDING_DOWNLOAD"

    @pytest.mark.asyncio
    async def test_user_by_steam_id_async(
        self, async_test_client: AsyncClient, postgres_connection: Connection
    ):
        """Test the user endpoint using async client."""
        # Create a user with tracking details using raw SQL
        user_result = postgres_connection.execute(
            text("""
            INSERT INTO "user" (steam_id, username, profile_picture_url, created_at)
            VALUES (:steam_id, :username, :profile_picture_url, NOW())
            RETURNING id
        """),
            {
                "steam_id": "76561198079047062",
                "username": "test_user",
                "profile_picture_url": "https://example.com/pic.jpg",
            },
        )
        user_row = user_result.fetchone()
        user_id = user_row.id

        postgres_connection.execute(
            text("""
            INSERT INTO tracking_details (authentication_code, initial_match_token, user_id)
            VALUES (:auth_code, :token, :user_id)
        """),
            {
                "auth_code": "auth123",
                "token": "token456",
                "user_id": user_id,
            },
        )
        postgres_connection.commit()

        # Test the endpoint
        steam_id = "76561198079047062"
        response = await async_test_client.get(f"/api/users/steamid/{steam_id}")

        assert response.status_code == 200
        data = response.json()
        assert data["steam_id"] == steam_id
        assert data["username"] == "test_user"
