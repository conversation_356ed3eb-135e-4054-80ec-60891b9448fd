"""Basic tests for the backend API."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import Connection, create_engine, text
from sqlalchemy.pool import StaticPool

from backend.database import get_connection_dependency, init_db
from backend.main import app


@pytest.fixture()
def test_connection():
    """Create an in-memory SQLite database for testing."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )

    # Initialize database tables using our init_db function
    import backend.database

    original_engine = backend.database.engine
    backend.database.engine = engine
    try:
        init_db()
    finally:
        backend.database.engine = original_engine

    with engine.connect() as conn:
        yield conn


@pytest.fixture()
def test_client(test_connection: Connection):
    """Create a test client with database dependency override."""

    def get_test_connection():
        yield test_connection

    app.dependency_overrides[get_connection_dependency] = get_test_connection

    with TestClient(app) as client:
        yield client

    app.dependency_overrides.clear()


class TestBasicAPI:
    """Test class for basic API functionality."""

    def test_health_endpoint(self, test_client: TestClient):
        """Test that the health endpoint works."""
        response = test_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "backend"
        assert data["version"] == "0.1.0"

    def test_user_by_steam_id_not_found(self, test_client: TestClient):
        """Test the user endpoint when user doesn't exist."""
        steam_id = "76561198079047062"
        response = test_client.get(f"/api/users/steamid/{steam_id}")
        assert response.status_code == 404
        assert "User not found" in response.json()["detail"]

    def test_user_registration_check_not_found(self, test_client: TestClient):
        """Test the user registration check when user doesn't exist."""
        steam_id = "76561198079047062"
        response = test_client.get(f"/api/users/steamid/{steam_id}/registered")
        assert response.status_code == 200
        assert response.json() is False

    def test_database_connection(self, test_connection: Connection):
        """Test that we can connect to the database."""
        result = test_connection.execute(text("SELECT 1 as test")).first()
        assert result.test == 1

    def test_database_tables_exist(self, test_connection: Connection):
        """Test that all required tables exist."""
        tables = [
            "user",
            "tracking_details",
            "demo_file",
            "processing_job",
            "match",
            "match_player",
            "match_round",
        ]

        for table in tables:
            # Check if table exists by trying to query it
            result = test_connection.execute(
                text(f"SELECT COUNT(*) FROM {table}")
            ).first()
            assert result[0] == 0  # Table should exist and be empty

    def test_demo_file_crud(self, test_connection: Connection):
        """Test demo file CRUD operations."""
        from backend import crud, schemas

        # Create a demo file
        demo_create = schemas.DemoFileCreate(
            match_id="test_match_123",
            file_path="/path/to/demo.dem",
            status=schemas.DemoFileStatus.PENDING_DOWNLOAD,
        )

        created_demo = crud.create_demo_file(demo_create, test_connection)
        assert created_demo.match_id == "test_match_123"
        assert created_demo.file_path == "/path/to/demo.dem"
        assert created_demo.status == schemas.DemoFileStatus.PENDING_DOWNLOAD
        assert created_demo.id is not None

        # Read the demo file by ID
        read_demo = crud.read_demo_file(created_demo.id, test_connection)
        assert read_demo.id == created_demo.id
        assert read_demo.match_id == created_demo.match_id

        # Read the demo file by match ID
        read_demo_by_match = crud.read_demo_file_by_match(
            "test_match_123", test_connection
        )
        assert read_demo_by_match.id == created_demo.id

        # Update the demo file
        updated_demo = crud.update_demo_file(
            created_demo.id,
            {"status": schemas.DemoFileStatus.DOWNLOADED.value},
            test_connection,
        )
        assert updated_demo.status == schemas.DemoFileStatus.DOWNLOADED

        # List demo files
        demo_files = crud.list_demo_files(test_connection)
        assert len(demo_files) == 1
        assert demo_files[0].id == created_demo.id

    def test_user_crud(self, test_connection: Connection):
        """Test user CRUD operations."""
        from backend import crud

        # Test user registration check for non-existent user
        is_registered = crud.is_user_registered("76561198079047062", test_connection)
        assert is_registered is False

        # Test reading non-existent user
        user = crud.read_user_by_steam_id(
            "76561198079047062", test_connection, raise_if_not_found=False
        )
        assert user is None
