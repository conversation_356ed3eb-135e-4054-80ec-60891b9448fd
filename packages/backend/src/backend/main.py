from contextlib import asynccontextmanager
from typing import Annotated

from fastapi import (
    APIRouter,
    Depends,
    FastAPI,
    File,
    Query,
    Response,
    UploadFile,
    status,
)

from backend import schemas
from backend.clients.steam_api import MatchInfo
from backend.database import init_db
from backend.dependencies import (
    cleanup_demo_processing_client,
    get_demo_service,
    get_match_service,
    get_user_service,
)
from backend.error_handling import Message
from backend.logging_config import configure_logging, get_logger
from backend.services.demo_service import DemoService
from backend.services.match_service import MatchService
from backend.services.user_service import UserService

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Initialize the application on startup.

    This function is called when the application starts up.
    It initializes the database and performs any other necessary setup.
    """
    # Configure logging
    configure_logging("INFO")
    logger.info("Starting backend service")

    # This is not truly async, but FastAPI expects an async context manager
    init_db()
    yield

    # Shutdown
    logger.info("Shutting down backend service")
    await cleanup_demo_processing_client()


app = FastAPI(lifespan=lifespan)

api_router = APIRouter()


# User management endpoints


@api_router.get("/users/steamid/{steam_id}")
async def read_user_by_steam_id(
    steam_id: str, user_service: Annotated[UserService, Depends(get_user_service)]
) -> schemas.UserRead | None:
    """Get a user by their Steam ID.

    Args:
        steam_id: The Steam ID of the user to retrieve
        user_service: User service instance

    Returns:
        The user with the specified Steam ID
    """
    return user_service.get_user_by_steam_id(steam_id)


@api_router.post("/users/register")
async def register_user(
    user: schemas.UserCreate,
    response: Response,
    user_service: Annotated[UserService, Depends(get_user_service)],
) -> dict[str, str] | None:
    """Register a new user.

    Args:
        user: User data to create
        response: FastAPI response object
        user_service: User service instance

    Returns:
        The created user or None if the user already exists
    """
    return await user_service.register_user(user, response)


@api_router.get("/users/steamid/{steam_id}/registered")
async def check_user_registered(
    steam_id: str, user_service: Annotated[UserService, Depends(get_user_service)]
) -> bool:
    """Check if a user is registered.

    Args:
        steam_id: The Steam ID to check
        user_service: User service instance

    Returns:
        True if the user is registered, False otherwise
    """
    return user_service.check_user_registered(steam_id)


@api_router.get(
    "/users/{user_id}",
    response_model=schemas.UserReadWithTracking,
    responses={404: {"model": Message}},
)
async def read_user(
    user_id: int, user_service: Annotated[UserService, Depends(get_user_service)]
) -> schemas.UserReadWithTracking:
    """Get a user by their ID.

    Args:
        user_id: The ID of the user to retrieve
        user_service: User service instance

    Returns:
        The user with the specified ID
    """
    return user_service.get_user(user_id)


@api_router.get(
    "/users/{user_id}/tracking",
    response_model=schemas.TrackingDetailsRead,
    responses={404: {"model": Message}},
)
async def read_user_tracking(
    user_id: int, user_service: Annotated[UserService, Depends(get_user_service)]
) -> schemas.TrackingDetailsRead:
    """Get tracking details for a user.

    Args:
        user_id: The ID of the user to get tracking details for
        user_service: User service instance

    Returns:
        The tracking details for the user
    """
    return user_service.get_user_tracking(user_id)


@api_router.post(
    "/users/{user_id}/tracking",
    response_model=schemas.TrackingDetailsRead,
    status_code=status.HTTP_201_CREATED,
    responses={
        404: {"model": Message},  # User not found
        409: {"model": Message},  # Tracking already exists
    },
)
async def create_user_tracking(
    user_id: int,
    tracking: schemas.TrackingDetailsCreate,
    user_service: Annotated[UserService, Depends(get_user_service)],
) -> schemas.TrackingDetailsRead:
    """Create tracking details for a user.

    Args:
        user_id: The ID of the user to create tracking details for
        tracking: The tracking details to create
        user_service: User service instance

    Returns:
        The created tracking details
    """
    return user_service.create_user_tracking(user_id, tracking)


# Demo management endpoints


@api_router.post(
    "/matches/{match_id}/demo",
    response_model=schemas.DemoFileRead,
    responses={404: {"model": Message}},
)
async def request_demo_download(
    match_id: str,
    demo_service: Annotated[DemoService, Depends(get_demo_service)],
) -> schemas.DemoFileRead:
    """Request a demo file download for a match.

    This endpoint will:
    1. Get match info from Steam
    2. Create a demo file record
    3. Queue the download in background

    Args:
        match_id: The match ID to download the demo for
        demo_service: Demo service instance

    Returns:
        The created demo file record
    """
    return await demo_service.request_demo_download(match_id)


@api_router.get(
    "/matches/{match_id}/demo/status",
    response_model=schemas.DemoFileRead,
    responses={404: {"model": Message}},
)
async def get_demo_status(
    match_id: str,
    demo_service: Annotated[DemoService, Depends(get_demo_service)],
) -> schemas.DemoFileRead:
    """Get the current status of a demo file.

    Args:
        match_id: The match ID to get demo status for
        demo_service: Demo service instance

    Returns:
        Demo file record with current status
    """
    return await demo_service.get_demo_status(match_id)


@api_router.post(
    "/demos/upload",
    response_model=schemas.DemoFileRead,
    status_code=status.HTTP_201_CREATED,
)
async def upload_demo_file(
    demo_service: Annotated[DemoService, Depends(get_demo_service)],
    match_id: Annotated[str, Query(description="The match ID to associate with the demo file")],
    demo_file: Annotated[UploadFile, File(description="The demo file to upload")],
) -> schemas.DemoFileRead:
    """Upload a demo file manually.

    This endpoint will:
    1. Save the uploaded demo file
    2. Create a demo file record
    3. Trigger automatic processing

    Args:
        match_id: The match ID to associate with the demo file
        demo_file: The uploaded demo file
        demo_service: Demo service instance

    Returns:
        The created demo file record

    Raises:
        HTTPException: If the file is not a valid demo file or there's an error saving it
    """
    return await demo_service.upload_demo_file(match_id, demo_file)


@api_router.get(
    "/demos",
    response_model=list[schemas.DemoFileRead],
)
async def list_demo_files(
    demo_service: Annotated[DemoService, Depends(get_demo_service)],
) -> list[schemas.DemoFileRead]:
    """List all demo files.

    Args:
        demo_service: Demo service instance

    Returns:
        List of all demo file records
    """
    return await demo_service.list_demo_files()


@api_router.get(
    "/matches/{match_id}/info",
    responses={404: {"model": Message}},
)
async def get_match_info(
    match_id: str,
    match_service: Annotated[MatchService, Depends(get_match_service)],
) -> MatchInfo:
    """Get information about a match from Steam.

    Args:
        match_id: The match ID to get information for
        match_service: Match service instance

    Returns:
        Match information from Steam API
    """
    return await match_service.get_match_info(match_id)


# Demo parsing endpoints


@api_router.post(
    "/matches/{match_id}/demo/parse",
    response_model=schemas.DemoFileRead,
    responses={404: {"model": Message}},
)
async def request_demo_parsing(
    match_id: str,
    demo_service: Annotated[DemoService, Depends(get_demo_service)],
) -> schemas.DemoFileRead:
    """Request parsing of a demo file.

    This endpoint will:
    1. Check if the demo file exists and is downloaded
    2. Queue the parsing in background using the demo processing microservice

    Args:
        match_id: The match ID to parse the demo for
        demo_service: Demo service instance

    Returns:
        The updated demo file record

    Raises:
        HTTPException: If demo file is not found or not downloaded
    """
    return await demo_service.request_demo_parsing(match_id)


@api_router.get(
    "/matches",
    response_model=list[schemas.MatchRead],
)
async def list_matches(
    match_service: Annotated[MatchService, Depends(get_match_service)],
) -> list[schemas.MatchRead]:
    """List all matches.

    Args:
        match_service: Match service instance

    Returns:
        List of all match records with players and rounds
    """
    return match_service.list_matches()


@api_router.get(
    "/matches/{match_id}",
    response_model=schemas.MatchRead,
    responses={404: {"model": Message}},
)
async def get_match(
    match_id: int,
    match_service: Annotated[MatchService, Depends(get_match_service)],
) -> schemas.MatchRead:
    """Get a match by ID.

    Args:
        match_id: The ID of the match to retrieve
        match_service: Match service instance

    Returns:
        Match record with players and rounds
    """
    return match_service.get_match(match_id)


@api_router.get(
    "/demos/{demo_id}/match",
    response_model=schemas.MatchRead,
    responses={404: {"model": Message}},
)
async def get_match_by_demo(
    demo_id: int,
    match_service: Annotated[MatchService, Depends(get_match_service)],
) -> schemas.MatchRead:
    """Get match data for a demo file.

    Args:
        demo_id: The ID of the demo file
        match_service: Match service instance

    Returns:
        Match record with players and rounds
    """
    return match_service.get_match_by_demo(demo_id)


@app.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint for monitoring and load balancers.

    Returns:
        Health status information
    """
    return {
        "status": "healthy",
        "service": "backend",
        "version": "0.1.0",
    }


app.include_router(api_router, prefix="/api")
