# Brainless Stats - Backend

FastAPI backend service for CS2 demo analysis platform.

## 🏗️ Architecture

- **FastAPI**: Modern Python web framework with automatic OpenAPI docs
- **SQLModel**: Type-safe database operations with Pydantic integration
- **FastStream**: Microservice communication with Redis
- **UV**: Fast Python package manager and dependency resolver
- **Typer**: Rich CLI interface for development operations

## 🚀 Quick Start

### Prerequisites

- Python 3.13+
- UV package manager
- Redis (for demo processing)

### Development Setup

```bash
# From project root
pnpm setup

# Or directly in backend
cd backend
uv sync
```

### Running the Backend

```bash
# From project root (recommended)
pnpm dev:backend

# Or using the CLI directly
cd backend
uv run brainless-cli dev backend

# Or traditional FastAPI dev server
uv run fastapi dev src/backend/main.py --host 0.0.0.0 --port 8000
```

## 🛠️ CLI Commands

The backend includes a comprehensive CLI built with Typer:

```bash
# Setup and development
uv run brainless-cli setup          # Setup environment
uv run brainless-cli dev             # Start full Docker environment
uv run brainless-cli dev backend     # Start only backend service

# Testing and quality
uv run brainless-cli test            # Run tests
uv run brainless-cli test --coverage # Run with coverage
uv run brainless-cli lint            # Run linting
uv run brainless-cli lint --fix      # Auto-fix issues
uv run brainless-cli format          # Format code

# Docker operations
uv run brainless-cli build           # Build Docker images
uv run brainless-cli logs            # View service logs
uv run brainless-cli shell backend   # Open backend shell
uv run brainless-cli status          # Show service status
uv run brainless-cli health          # Check service health

# Cleanup
uv run brainless-cli clean           # Clean containers
uv run brainless-cli clean --all     # Clean everything including volumes
```

## 📁 Project Structure

```
backend/
├── src/backend/
│   ├── main.py              # FastAPI application
│   ├── cli.py               # Typer CLI interface
│   ├── config.py            # Configuration management
│   ├── database.py          # Database setup
│   ├── models.py            # SQLModel data models
│   ├── crud.py              # Database operations
│   ├── clients/             # External service clients
│   │   ├── demo_processing_client.py
│   │   └── steam_api.py
│   └── ...
├── demo_processing/         # Demo processing microservice
├── Dockerfile.dev           # Development container
├── Dockerfile.prod          # Production container
├── pyproject.toml           # Dependencies and configuration
└── README.md               # This file
```

## 🔧 Configuration

### Environment Variables

Copy and customize the environment file:

```bash
cp .env.example .env
```

Key configuration options:

```bash
# Environment
ENV=development

# Database
DATABASE_URI=sqlite:///./database.db

# Steam API
STEAM_API_KEY=your_steam_api_key_here

# Demo Storage
DEMO_STORAGE_PATH=./storage/demos
DEMO_RETENTION_DAYS=7

# Redis
REDIS_URL=redis://localhost:6379
```

### UV Workspace

The backend uses UV workspace for managing multiple Python packages:

- `backend/` - Main FastAPI application
- `backend/demo_processing/` - Demo processing microservice

## 🧪 Testing

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run brainless-cli test --coverage

# Run specific test file
uv run pytest tests/test_models.py

# Run with verbose output
uv run pytest -v
```

## 🔍 Code Quality

### Linting and Formatting

```bash
# Check code quality
uv run ruff check

# Auto-fix issues
uv run ruff check --fix

# Format code
uv run ruff format

# Type checking
uv run basedpyright
```

### Pre-commit Hooks

The project uses Ruff for linting and formatting with the following configuration:

- **Target**: Python 3.13
- **Style**: Google docstring convention
- **Rules**: Comprehensive rule set including security, performance, and style checks

## 🐳 Docker

### Development

```bash
# Build development image
docker build -f Dockerfile.dev -t brainless-stats-backend:dev .

# Run with hot reloading
docker run -p 8000:8000 -v $(pwd)/src:/app/src brainless-stats-backend:dev
```

### Production

```bash
# Build production image
docker build -f Dockerfile.prod -t brainless-stats-backend:prod .

# Run production container
docker run -p 8000:8000 brainless-stats-backend:prod
```

## 📚 API Documentation

When running the backend, API documentation is available at:

- **Swagger UI**: <http://localhost:8000/docs>
- **ReDoc**: <http://localhost:8000/redoc>
- **OpenAPI JSON**: <http://localhost:8000/openapi.json>

## 🔗 Integration

### Demo Processing

The backend integrates with the demo processing microservice via FastStream and Redis:

```python
from backend.clients.demo_processing_client import DemoProcessingClient

client = DemoProcessingClient()
result = await client.process_demo(demo_file_path)
```

### Steam API

Steam API integration for match data:

```python
from backend.clients.steam_api import SteamAPI

steam_api = SteamAPI(api_key="your_key")
match_info = await steam_api.get_match_info(match_id)
```

## 🚀 Deployment

### Production Checklist

- [ ] Set `ENV=production`
- [ ] Configure production database
- [ ] Set secure `SECRET_KEY`
- [ ] Configure Redis connection
- [ ] Set up SSL certificates
- [ ] Configure monitoring and logging
- [ ] Set resource limits

### Health Checks

The backend provides health check endpoints:

- **Health**: `GET /health` - Basic health status
- **Detailed**: `GET /api/health` - Detailed service status

## 🤝 Contributing

1. Follow the code style (Ruff formatting)
1. Add tests for new features
1. Update documentation
1. Run the full test suite
1. Check type annotations

```bash
# Before committing
uv run brainless-cli lint --fix
uv run brainless-cli format
uv run brainless-cli test
```
