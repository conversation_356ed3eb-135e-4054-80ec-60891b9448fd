#!/usr/bin/env python3
"""Migration script to test the new raw SQL + SQLAlchemy Core setup."""

import sys
from pathlib import Path

# Add the backend source to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))


from backend.crud import create_demo_file, read_user_by_steam_id
from backend.database import get_connection, init_db
from backend.schemas import DemoFileCreate, DemoFileStatus
from sqlalchemy import text


def test_migration():
    """Test the migration by creating some sample data."""
    print("🚀 Testing Raw SQL + SQLAlchemy Core migration...")

    # Initialize the database
    print("📊 Initializing database...")
    init_db()
    print("✅ Database initialized successfully!")

    # Test basic connection
    with get_connection() as conn:
        result = conn.execute(text("SELECT 1 as test"))
        test_value = result.fetchone().test
        assert test_value == 1
        print("✅ Database connection test passed!")

    # Test basic operations
    print("👤 Testing basic operations...")
    with get_connection() as conn:
        # Test reading non-existent user
        user = read_user_by_steam_id(
            "76561198000000000", conn, raise_if_not_found=False
        )
        assert user is None
        print("✅ Non-existent user read test passed!")

        # Test demo file creation
        print("📁 Testing demo file operations...")
        demo_create = DemoFileCreate(
            match_id="test_match_123",
            file_path="/path/to/demo.dem",
            status=DemoFileStatus.PENDING_DOWNLOAD,
        )

        demo = create_demo_file(demo_create, conn)
        assert demo.id is not None
        assert demo.match_id == "test_match_123"
        assert demo.status == DemoFileStatus.PENDING_DOWNLOAD
        print("✅ Demo file creation test passed!")

    print("🎉 All tests passed! Migration successful!")
    print("\n📋 Migration Summary:")
    print("  ✅ Removed SQLModel dependency")
    print("  ✅ Added SQLAlchemy Core for database operations")
    print("  ✅ Created separate Pydantic schemas for validation")
    print("  ✅ Migrated to raw SQL queries")
    print("  ✅ Updated database connection management")
    print("\n🔧 Next steps:")
    print("  1. Complete migration of remaining CRUD functions")
    print("  2. Update all API endpoints to use new schemas")
    print("  3. Update tests to use new connection dependency")
    print("  4. Test with your actual Steam API key")


if __name__ == "__main__":
    test_migration()
