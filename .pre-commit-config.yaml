# Pre-commit configuration for brainless-stats
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Markdown linting and fixing
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.45.0
    hooks:
      - id: markdownlint-fix

  # Basic pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-merge-conflict
      - id: check-json
      - id: check-yaml
        args: ["--unsafe"] # Allow custom YAML tags
      - id: check-added-large-files
        args: ["--maxkb=1000"] # Limit files to 1MB

  # Python project validation
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.24.1
    hooks:
      - id: validate-pyproject

  # Security scanning for secrets (simple keyword detection)
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: detect-private-key

  # Spell checking
  - repo: https://github.com/codespell-project/codespell
    rev: v2.4.1
    hooks:
      - id: codespell
        args: ["--write-changes"]
        exclude: ^(.*\.lock|.*\.min\.js|.*\.min\.css|pnpm-lock\.yaml|frontend/playwright-report/.*|.*package-lock\.json)$

  # Markdown formatting
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.22
    hooks:
      - id: mdformat

  # Markdown link checking
  - repo: https://github.com/tcort/markdown-link-check
    rev: v3.13.7
    hooks:
      - id: markdown-link-check
        args: ["--config", ".markdown-link-check.json"]

  # YAML formatting with Prettier
  - repo: https://github.com/rbubley/mirrors-prettier
    rev: v3.5.3
    hooks:
      - id: prettier
        types: [yaml]

  # Python linting and formatting with Ruff
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.13
    hooks:
      # Run the linter (only on staged files)
      - id: ruff
        args: [--fix]
        exclude: ^(packages/backend/migrate_to_raw_sql\.py|tasks\.py)$
      # Run the formatter
      - id: ruff-format
        exclude: ^(packages/backend/migrate_to_raw_sql\.py|tasks\.py)$

  # Python type checking with basedpyright (only on staged files)
  - repo: local
    hooks:
      - id: basedpyright
        name: basedpyright
        entry: uv run basedpyright --level error
        language: system
        types: [python]
        require_serial: true
        exclude: ^(packages/backend/migrate_to_raw_sql\.py|tasks\.py)$

  # GitHub Actions linting with actionlint
  - repo: https://github.com/rhysd/actionlint
    rev: v1.7.7
    hooks:
      - id: actionlint

  # Frontend JavaScript/TypeScript linting and formatting
  - repo: local
    hooks:
      # ESLint for frontend
      - id: eslint-frontend
        name: ESLint (Frontend)
        entry: bash -c 'cd frontend && pnpm lint'
        language: system
        files: ^frontend/.*\.(js|ts|svelte)$
        require_serial: true
        pass_filenames: false

      # Prettier for frontend
      - id: prettier-frontend
        name: Prettier (Frontend)
        entry: bash -c 'cd frontend && pnpm format'
        language: system
        files: ^frontend/.*\.(js|ts|svelte|json|css|html|md)$
        require_serial: true
        pass_filenames: false

      # Svelte type checking
      - id: svelte-check
        name: Svelte Check
        entry: bash -c 'cd frontend && pnpm check'
        language: system
        files: ^frontend/.*\.(js|ts|svelte)$
        require_serial: true
        pass_filenames: false

  # Package.json validation for frontend
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-json
        files: ^frontend/package\.json$

  # UV dependency management
  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.7.12
    hooks:
      # Compile requirements
      - id: uv-lock
