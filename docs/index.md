# Brainless Stats Documentation

## Overview

Brainless Stats is a web application for analyzing CS2 (Counter-Strike 2) match demos, providing detailed statistics, visualizations, and insights to help players improve their gameplay.

## Documentation Index

### Getting Started

- [README](README.md) - Project overview and introduction
- [Setup and Installation](setup.md) - Setting up the development environment
- [Features and Roadmap](features-and-roadmap.md) - Current features and future plans

### Architecture and Design

- [Architecture Overview](architecture.md) - System architecture and component interactions
- [API Reference](api-reference.md) - Backend API documentation
- [Frontend Guide](frontend.md) - Frontend application structure and components
- [Demo Processing](demo-processing.md) - Demo processing system documentation

### Features

- [Manual Demo Upload](manual-demo-upload.md) - Documentation for the manual demo upload feature

### Development

- [Contributing](contributing.md) - Guidelines for contributing to the project

## Project Structure

The project is organized into several main components:

```
brainless-stats/
├── backend/             # FastAPI backend application
│   ├── src/             # Source code
│   ├── tests/           # Test files
│   └── docs/            # Backend-specific documentation
├── frontend/            # Svelte frontend application
│   ├── src/             # Source code
│   └── tests/           # Test files
├── demo_processing/     # Demo file processing library
└── docs/                # Project documentation
```

## Quick Links

- [GitHub Repository](https://github.com/maxnoller/brainless-stats)
- [Backend API Documentation](http://localhost:8000/docs) (when running locally)
- [Frontend Development Server](http://localhost:5173) (when running locally)

## Getting Help

If you need help with Brainless Stats, you can:

1. Check the documentation in this directory
1. Look for answers in the GitHub repository issues
1. Create a new issue on GitHub if you can't find an answer

## Contributing

We welcome contributions to both the code and documentation. See the [Contributing Guide](contributing.md) for more information.
