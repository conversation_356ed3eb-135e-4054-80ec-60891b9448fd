# Setup and Installation

This guide provides comprehensive instructions for setting up the Brainless Stats development environment using our modern monorepo structure.

## 🚀 Quick Start

For the fastest setup experience:

```bash
# Clone the repository
<NAME_EMAIL>:maxnoller/brainless-stats.git
cd brainless-stats

# One-command setup
pnpm setup

# Start development environment
pnpm dev
```

That's it! The application will be available at:

- **Frontend**: <http://localhost:3000>
- **Backend API**: <http://localhost:8000>
- **API Docs**: <http://localhost:8000/docs>

## 📋 Prerequisites

### Required Software

- **Node.js** 18+ and **pnpm** 8+
- **Python** 3.13+ and **uv**
- **Docker** and **Docker Compose**
- **Git** for version control

### Installation Commands

```bash
# Install Node.js and pnpm
curl -fsSL https://get.pnpm.io/install.sh | sh

# Install Python and uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install Docker (varies by OS)
# See: https://docs.docker.com/get-docker/
```

## 🏗️ Detailed Setup

### 1. Clone and Initial Setup

```bash
# Clone the repository
<NAME_EMAIL>:maxnoller/brainless-stats.git
cd brainless-stats

# Setup everything (installs dependencies, creates env files)
pnpm setup
```

### 2. Environment Configuration

The setup command creates environment files from templates. Customize them as needed:

#### Root Environment (`.env`)

```bash
# Environment
ENV=development

# Backend API URL
BACKEND_API_URL=http://localhost:8000

# Docker configuration
COMPOSE_PROJECT_NAME=brainless-stats-dev
```

#### Backend Environment (`backend/.env`)

```bash
# Database
DATABASE_URI=sqlite:///./database.db

# Steam API (get from https://steamcommunity.com/dev/apikey)
STEAM_API_KEY=your_steam_api_key_here

# Demo Storage
DEMO_STORAGE_PATH=./storage/demos
DEMO_RETENTION_DAYS=7

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=dev-secret-key-not-for-production
```

#### Frontend Environment (`frontend/.env`)

```bash
# Database (frontend local database)
DATABASE_URL=local.db

# Backend API
BACKEND_API_URL=http://localhost:8000

# Node Environment
NODE_ENV=development
```

### 3. Development Options

Choose your preferred development approach:

#### Option A: Full Docker Environment (Recommended for Production-like Testing)

```bash
# Start all services with Docker
pnpm dev:full

# View logs
pnpm logs

# Stop services
pnpm stop
```

#### Option B: Hybrid Development (Recommended for Active Development)

```bash
# Start frontend and backend directly (faster iteration)
pnpm dev

# Or start services individually
pnpm dev:frontend    # Terminal 1
pnpm dev:backend     # Terminal 2
```

#### Option C: Individual Services

```bash
# Frontend only
pnpm dev:frontend

# Backend only
pnpm dev:backend

# Demo processing only
pnpm dev:demo-processing
```

## 🛠️ Task Runner Options

The project provides multiple task runners for different preferences:

### Universal Task Runner (Cross-platform)

```bash
python tasks.py --help
python tasks.py dev
python tasks.py test --coverage
```

### pnpm Scripts (Recommended)

```bash
pnpm --help
pnpm dev
pnpm test
pnpm lint:fix
```

### Python CLI (Backend Operations)

```bash
cd backend && uv run brainless-cli --help
uv run brainless-cli dev backend
uv run brainless-cli shell redis
```

### Node.js Task Runner (Frontend Operations)

```bash
node scripts/tasks.mjs --help
node scripts/tasks.mjs dev
node scripts/tasks.mjs test:e2e
```

## 🔧 Service Details

### Frontend (SvelteKit)

- **Port**: 3000
- **Hot Reloading**: Enabled
- **Type Checking**: Real-time with TypeScript
- **Database**: Local SQLite for user sessions

### Backend (FastAPI)

- **Port**: 8000
- **Hot Reloading**: Enabled with `fastapi dev`
- **API Docs**: <http://localhost:8000/docs>
- **Database**: SQLite (development) / PostgreSQL (production)

### Demo Processing (FastStream)

- **Message Broker**: Redis
- **Concurrency**: Configurable parallel processing
- **Health Checks**: Built-in monitoring

### Redis

- **Port**: 6379
- **Purpose**: Message queuing and caching
- **Persistence**: Enabled in production

## 🧪 Testing

### Run All Tests

```bash
# All tests with coverage
pnpm test:coverage

# Quick test run
pnpm test
```

### Individual Test Suites

```bash
# Frontend tests
pnpm test:frontend
pnpm test:e2e              # End-to-end tests

# Backend tests
pnpm test:backend
cd backend && uv run brainless-cli test --coverage
```

### Test Structure

```
tests/
├── frontend/
│   ├── unit/              # Component tests
│   ├── integration/       # API integration tests
│   └── e2e/              # End-to-end tests
└── backend/
    ├── unit/              # Unit tests
    ├── integration/       # Service integration tests
    └── fixtures/          # Test data
```

## 🔍 Code Quality

### Linting and Formatting

```bash
# Auto-fix all issues
pnpm lint:fix
pnpm format

# Check only
pnpm lint
```

### Type Checking

```bash
# Frontend type checking
pnpm --filter frontend check

# Backend type checking
cd backend && uv run basedpyright
```

## 🐳 Docker Development

### Full Docker Environment

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up --build

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### Individual Service Containers

```bash
# Backend only
cd backend && uv run brainless-cli dev backend

# Demo processing only
cd backend && uv run brainless-cli dev demo-processing

# Shell access
cd backend && uv run brainless-cli shell backend
```

## 🚀 Production Build

### Build All Services

```bash
# Build everything
pnpm build

# Build Docker images
pnpm build:docker
```

### Individual Builds

```bash
# Frontend build
pnpm build:frontend

# Backend build (Docker)
cd backend && uv run brainless-cli build
```

## 🔄 Development Workflow

### Daily Development

1. **Start services**: `pnpm dev`
1. **Make changes**: Edit code with hot reloading
1. **Run tests**: `pnpm test` (as needed)
1. **Check quality**: `pnpm lint:fix` (before committing)
1. **Commit changes**: Follow conventional commits

### Working with APIs

1. **Generate types**: `pnpm schema:generate` (after backend changes)
1. **Test endpoints**: Use <http://localhost:8000/docs>
1. **Integration tests**: `pnpm test:e2e`

### Database Changes

```bash
# Frontend database
cd frontend
pnpm db:generate           # Generate migrations
pnpm db:migrate           # Apply migrations
pnpm db:studio            # Open database studio

# Backend database
cd backend
# Database migrations handled by SQLModel/Alembic
```

## 🛠️ Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Check what's using the port
lsof -i :3000
lsof -i :8000

# Kill the process
kill -9 <PID>
```

#### Docker Issues

```bash
# Clean up Docker
pnpm clean:all

# Rebuild containers
pnpm dev:full
```

#### Dependency Issues

```bash
# Clean and reinstall
rm -rf node_modules frontend/node_modules
rm -rf backend/.venv
pnpm setup
```

#### Environment Issues

```bash
# Reset environment files
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

### Getting Help

1. **Check logs**: `pnpm logs` or `pnpm status`
1. **Health check**: `pnpm health`
1. **Documentation**: Check service-specific README files
1. **Issues**: Search GitHub issues for similar problems

## 📚 Next Steps

After setup:

1. **Explore the codebase**: Check individual service README files
1. **Run tests**: Ensure everything works
1. **Make a change**: Try modifying a component
1. **Read documentation**: Check [docs/](.) for detailed guides
1. **Contribute**: See [CONTRIBUTING.md](../CONTRIBUTING.md)

## 🔗 Useful Links

- **API Documentation**: <http://localhost:8000/docs>
- **Frontend**: <http://localhost:3000>
- **Architecture Guide**: [architecture.md](architecture.md)
- **Contributing Guide**: [../CONTRIBUTING.md](../CONTRIBUTING.md)
- **Deployment Guide**: [../DEPLOYMENT.md](../DEPLOYMENT.md)
