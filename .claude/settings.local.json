{"permissions": {"allow": ["Bash(pnpm *)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:keepachangelog.com)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "<PERSON><PERSON>(uv *)", "<PERSON><PERSON>(gh issue view:*)", "<PERSON><PERSON>(gh issue edit:*)", "Bash(gh issue list:*)", "Bash(ls:*)", "Edit(*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs"], "deny": []}}