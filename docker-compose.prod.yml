version: "3.8"

services:
  # Redis for demo processing and caching
  redis:
    image: redis:7-alpine
    container_name: brainless-stats-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data_prod:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - brainless-stats-prod
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # Demo Processing Microservice
  demo-processing:
    build:
      context: ./packages/demo-processing
      dockerfile: Dockerfile
      target: production
    container_name: brainless-stats-demo-processing-prod
    environment:
      - DEMO_PROCESSING_REDIS_URL=redis://redis:6379
      - DEMO_PROCESSING_LOG_LEVEL=INFO
      - DEMO_PROCESSING_SERVICE_NAME=demo-processing-prod
      - DEMO_PROCESSING_MAX_CONCURRENT_PROCESSING=4
      - DEMO_PROCESSING_PROCESSING_TIMEOUT_SECONDS=600
      - DEMO_PROCESSING_EXECUTOR_MAX_WORKERS=4
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - demo_storage_prod:/workspace/demo_processing/demo_files:ro
    restart: unless-stopped
    networks:
      - brainless-stats-prod
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import redis; redis.Redis(host='redis', port=6379, socket_connect_timeout=1).ping()",
        ]
      interval: 60s
      timeout: 15s
      start_period: 60s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: "2.0"
        reservations:
          memory: 1G
          cpus: "1.0"

  # Main Backend API
  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile.prod
    container_name: brainless-stats-backend-prod
    ports:
      - "8000:8000"
    environment:
      - ENV=production
      - DATABASE_URI=sqlite:///./data/database.db
      - DEMO_STORAGE_PATH=/app/data/demos
      - REDIS_URL=redis://redis:6379
    depends_on:
      redis:
        condition: service_healthy
      demo-processing:
        condition: service_healthy
    volumes:
      - backend_data_prod:/app/data
    restart: unless-stopped
    networks:
      - brainless-stats-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 60s
      timeout: 15s
      start_period: 60s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "1.0"
        reservations:
          memory: 512M
          cpus: "0.5"

  # Frontend with API Gateway
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: brainless-stats-frontend-prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=/app/data/local.db
      - BACKEND_API_URL=http://backend:8000
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - frontend_data_prod:/app/data
    restart: unless-stopped
    networks:
      - brainless-stats-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 60s
      timeout: 15s
      start_period: 60s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # Nginx reverse proxy (optional for production)
  nginx:
    image: nginx:alpine
    container_name: brainless-stats-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - brainless-stats-prod
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: "0.25"
        reservations:
          memory: 64M
          cpus: "0.1"

volumes:
  redis_data_prod:
    driver: local
  demo_storage_prod:
    driver: local
  backend_data_prod:
    driver: local
  frontend_data_prod:
    driver: local

networks:
  brainless-stats-prod:
    driver: bridge
